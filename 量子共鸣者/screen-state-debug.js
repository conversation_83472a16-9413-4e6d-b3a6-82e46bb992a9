/**
 * 屏幕状态调试脚本
 * 用于检查和修复屏幕显示状态异常问题
 */

(function() {
    'use strict';

    console.log('🔍 开始屏幕状态调试...');

    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkScreenStates);
    } else {
        checkScreenStates();
    }

    function checkScreenStates() {
        console.log('📱 检查所有屏幕状态...');

        // 获取所有屏幕元素
        const screens = document.querySelectorAll('.screen');
        
        screens.forEach((screen, index) => {
            const screenId = screen.id || `screen-${index}`;
            const computedStyle = window.getComputedStyle(screen);
            const hasActiveClass = screen.classList.contains('active');
            
            console.log(`📱 屏幕 ${screenId}:`);
            console.log(`  - 类名: ${screen.className}`);
            console.log(`  - 是否有 active 类: ${hasActiveClass}`);
            console.log(`  - 内联样式 display: ${screen.style.display || '(无)'}`);
            console.log(`  - 内联样式 opacity: ${screen.style.opacity || '(无)'}`);
            console.log(`  - 内联样式 visibility: ${screen.style.visibility || '(无)'}`);
            console.log(`  - 计算样式 display: ${computedStyle.display}`);
            console.log(`  - 计算样式 opacity: ${computedStyle.opacity}`);
            console.log(`  - 计算样式 visibility: ${computedStyle.visibility}`);
            
            // 检查是否存在异常状态
            const isVisibleByInlineStyle = (
                screen.style.display === 'flex' || 
                screen.style.display === 'block'
            ) && (
                screen.style.opacity === '1' || 
                screen.style.opacity === ''
            ) && (
                screen.style.visibility === 'visible' || 
                screen.style.visibility === ''
            );
            
            const isVisibleByComputedStyle = (
                computedStyle.display !== 'none' &&
                computedStyle.opacity !== '0' &&
                computedStyle.visibility !== 'hidden'
            );
            
            if (isVisibleByInlineStyle && !hasActiveClass) {
                console.warn(`⚠️ 异常状态检测: ${screenId} 通过内联样式显示但没有 active 类`);
                fixScreenState(screen, screenId);
            } else if (isVisibleByComputedStyle && !hasActiveClass) {
                console.warn(`⚠️ 异常状态检测: ${screenId} 显示但没有 active 类`);
                fixScreenState(screen, screenId);
            } else if (hasActiveClass && !isVisibleByComputedStyle) {
                console.warn(`⚠️ 异常状态检测: ${screenId} 有 active 类但不可见`);
            } else {
                console.log(`✅ ${screenId} 状态正常`);
            }
            
            console.log('---');
        });

        // 特别检查关卡选择屏幕
        checkLevelSelectScreen();
    }

    function checkLevelSelectScreen() {
        console.log('🎯 特别检查关卡选择屏幕...');
        
        const levelSelectScreen = document.getElementById('levelSelectScreen');
        if (!levelSelectScreen) {
            console.warn('⚠️ 关卡选择屏幕元素未找到');
            return;
        }

        const computedStyle = window.getComputedStyle(levelSelectScreen);
        const hasActiveClass = levelSelectScreen.classList.contains('active');
        
        console.log('🎯 关卡选择屏幕详细状态:');
        console.log(`  - ID: ${levelSelectScreen.id}`);
        console.log(`  - 完整类名: "${levelSelectScreen.className}"`);
        console.log(`  - 是否有 active 类: ${hasActiveClass}`);
        console.log(`  - 内联 display: "${levelSelectScreen.style.display}"`);
        console.log(`  - 内联 opacity: "${levelSelectScreen.style.opacity}"`);
        console.log(`  - 内联 visibility: "${levelSelectScreen.style.visibility}"`);
        console.log(`  - 计算 display: "${computedStyle.display}"`);
        console.log(`  - 计算 opacity: "${computedStyle.opacity}"`);
        console.log(`  - 计算 visibility: "${computedStyle.visibility}"`);
        console.log(`  - 计算 position: "${computedStyle.position}"`);
        console.log(`  - 计算 z-index: "${computedStyle.zIndex}"`);

        // 检查是否应该隐藏
        if (!hasActiveClass) {
            const shouldBeHidden = (
                computedStyle.opacity === '0' &&
                computedStyle.visibility === 'hidden'
            );
            
            if (!shouldBeHidden) {
                console.warn('⚠️ 关卡选择屏幕应该隐藏但当前可见');
                fixScreenState(levelSelectScreen, 'levelSelectScreen');
            } else {
                console.log('✅ 关卡选择屏幕状态正确（已隐藏）');
            }
        } else {
            console.log('✅ 关卡选择屏幕状态正确（已激活）');
        }
    }

    function fixScreenState(screen, screenId) {
        console.log(`🔧 修复屏幕状态: ${screenId}`);
        
        // 清除内联样式，让CSS类控制
        screen.style.display = '';
        screen.style.opacity = '';
        screen.style.visibility = '';
        
        // 移除active类（如果不应该显示）
        screen.classList.remove('active');
        
        console.log(`✅ ${screenId} 状态已修复`);
    }

    // 提供全局调试函数
    window.debugScreenStates = checkScreenStates;
    window.fixAllScreenStates = function() {
        const screens = document.querySelectorAll('.screen');
        screens.forEach((screen, index) => {
            const screenId = screen.id || `screen-${index}`;
            if (!screen.classList.contains('active')) {
                fixScreenState(screen, screenId);
            }
        });
    };

    console.log('🔍 屏幕状态调试脚本加载完成');
    console.log('💡 可以使用 debugScreenStates() 重新检查状态');
    console.log('💡 可以使用 fixAllScreenStates() 修复所有异常状态');
})();
